{"roots": ["foodking"], "packages": [{"name": "foodking", "version": "1.0.10+19", "dependencies": ["animated_bottom_navigation_bar", "another_stepper", "cached_network_image", "connectivity_plus", "contained_tab_bar_view", "country_picker", "cupertino_icons", "custom_map_markers", "dropdown_button2", "easy_stepper", "expandable", "expansion_tile_card", "firebase_core", "firebase_messaging", "flutter", "flutter_chat_ui", "flutter_html", "flutter_inappwebview", "flutter_launcher_icons", "flutter_local_notifications", "flutter_screenutil", "flutter_staggered_grid_view", "flutter_svg", "flutter_typeahead", "fluttertoast", "geocoding", "geolocator", "get", "get_storage", "google_api_headers", "google_fonts", "google_maps_flutter", "google_maps_flutter_ios", "im_stepper", "image_picker", "lottie", "map_launcher", "path_provider", "pinput", "rflutter_alert", "shared_preferences", "shimmer", "timeline_tile", "toggle_switch", "url_launcher", "webview_flutter"], "devDependencies": ["flutter_lints", "flutter_test"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["clock", "collection", "fake_async", "flutter", "leak_tracker_flutter_testing", "matcher", "meta", "path", "stack_trace", "stream_channel", "test_api", "vector_math"]}, {"name": "flutter_lints", "version": "5.0.0", "dependencies": ["lints"]}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "google_api_headers", "version": "4.4.3", "dependencies": ["flutter", "package_info_plus"]}, {"name": "google_fonts", "version": "6.2.1", "dependencies": ["crypto", "flutter", "http", "path_provider"]}, {"name": "google_maps_flutter_ios", "version": "2.15.1", "dependencies": ["flutter", "google_maps_flutter_platform_interface", "stream_transform"]}, {"name": "flutter_html", "version": "3.0.0", "dependencies": ["collection", "csslib", "flutter", "html", "list_counter"]}, {"name": "easy_stepper", "version": "0.8.5+1", "dependencies": ["flutter", "lottie"]}, {"name": "map_launcher", "version": "3.5.0", "dependencies": ["flutter"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "flutter_inappwebview", "version": "6.1.5", "dependencies": ["flutter", "flutter_inappwebview_android", "flutter_inappwebview_ios", "flutter_inappwebview_macos", "flutter_inappwebview_platform_interface", "flutter_inappwebview_web", "flutter_inappwebview_windows"]}, {"name": "flutter_staggered_grid_view", "version": "0.7.0", "dependencies": ["flutter"]}, {"name": "pinput", "version": "5.0.1", "dependencies": ["flutter", "universal_platform"]}, {"name": "fluttertoast", "version": "8.2.12", "dependencies": ["flutter", "flutter_web_plugins", "web"]}, {"name": "im_stepper", "version": "1.0.1+1", "dependencies": ["flutter"]}, {"name": "rflutter_alert", "version": "2.0.7", "dependencies": ["flutter"]}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "flutter_launcher_icons", "version": "0.14.3", "dependencies": ["args", "checked_yaml", "cli_util", "image", "json_annotation", "path", "yaml"]}, {"name": "flutter_local_notifications", "version": "18.0.1", "dependencies": ["clock", "flutter", "flutter_local_notifications_linux", "flutter_local_notifications_platform_interface", "timezone"]}, {"name": "firebase_core", "version": "3.13.0", "dependencies": ["firebase_core_platform_interface", "firebase_core_web", "flutter", "meta"]}, {"name": "firebase_messaging", "version": "15.2.5", "dependencies": ["firebase_core", "firebase_core_platform_interface", "firebase_messaging_platform_interface", "firebase_messaging_web", "flutter", "meta"]}, {"name": "webview_flutter", "version": "4.10.0", "dependencies": ["flutter", "webview_flutter_android", "webview_flutter_platform_interface", "webview_flutter_wkwebview"]}, {"name": "url_launcher", "version": "6.3.1", "dependencies": ["flutter", "url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows"]}, {"name": "connectivity_plus", "version": "6.1.3", "dependencies": ["collection", "connectivity_plus_platform_interface", "flutter", "flutter_web_plugins", "meta", "nm", "web"]}, {"name": "custom_map_markers", "version": "0.0.2+1", "dependencies": ["collection", "flutter", "google_maps_flutter", "synchronized"]}, {"name": "flutter_typeahead", "version": "5.2.0", "dependencies": ["flutter", "flutter_keyboard_visibility", "pointer_interceptor"]}, {"name": "geocoding", "version": "3.0.0", "dependencies": ["flutter", "geocoding_android", "geocoding_ios", "geocoding_platform_interface"]}, {"name": "geolocator", "version": "13.0.4", "dependencies": ["flutter", "geolocator_android", "geolocator_apple", "geolocator_platform_interface", "geolocator_web", "geolocator_windows"]}, {"name": "google_maps_flutter", "version": "2.12.1", "dependencies": ["flutter", "google_maps_flutter_android", "google_maps_flutter_ios", "google_maps_flutter_platform_interface", "google_maps_flutter_web"]}, {"name": "cached_network_image", "version": "3.4.1", "dependencies": ["cached_network_image_platform_interface", "cached_network_image_web", "flutter", "flutter_cache_manager", "octo_image"]}, {"name": "shimmer", "version": "3.0.0", "dependencies": ["flutter"]}, {"name": "flutter_chat_ui", "version": "1.6.15", "dependencies": ["diffutil_dart", "equatable", "flutter", "flutter_chat_types", "flutter_link_previewer", "flutter_parsed_text", "intl", "meta", "photo_view", "scroll_to_index", "url_launcher", "visibility_detector"]}, {"name": "dropdown_button2", "version": "2.3.9", "dependencies": ["flutter", "meta"]}, {"name": "country_picker", "version": "2.0.27", "dependencies": ["collection", "flutter", "universal_io"]}, {"name": "expandable", "version": "5.0.1", "dependencies": ["flutter"]}, {"name": "expansion_tile_card", "version": "3.0.0", "dependencies": ["flutter"]}, {"name": "another_stepper", "version": "1.2.2", "dependencies": ["flutter"]}, {"name": "toggle_switch", "version": "2.3.0", "dependencies": ["flutter"]}, {"name": "contained_tab_bar_view", "version": "0.8.0", "dependencies": ["container_tab_indicator", "flutter"]}, {"name": "timeline_tile", "version": "2.0.0", "dependencies": ["flutter"]}, {"name": "lottie", "version": "3.3.1", "dependencies": ["archive", "flutter", "http", "path", "vector_math"]}, {"name": "flutter_svg", "version": "2.0.17", "dependencies": ["flutter", "http", "vector_graphics", "vector_graphics_codec", "vector_graphics_compiler"]}, {"name": "get_storage", "version": "2.1.1", "dependencies": ["flutter", "get", "path_provider"]}, {"name": "image_picker", "version": "1.1.2", "dependencies": ["flutter", "image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_platform_interface", "image_picker_windows"]}, {"name": "flutter_screenutil", "version": "5.9.3", "dependencies": ["flutter"]}, {"name": "animated_bottom_navigation_bar", "version": "1.4.0", "dependencies": ["flutter"]}, {"name": "get", "version": "4.7.2", "dependencies": ["flutter", "web"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "leak_tracker_flutter_testing", "version": "3.0.10", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.2.0", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.6", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "lints", "version": "5.1.1", "dependencies": []}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "package_info_plus", "version": "8.3.0", "dependencies": ["clock", "ffi", "flutter", "flutter_web_plugins", "http", "meta", "package_info_plus_platform_interface", "path", "web", "win32"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "http", "version": "1.3.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "stream_transform", "version": "2.1.1", "dependencies": []}, {"name": "google_maps_flutter_platform_interface", "version": "2.11.1", "dependencies": ["collection", "flutter", "plugin_platform_interface", "stream_transform"]}, {"name": "list_counter", "version": "1.0.2", "dependencies": []}, {"name": "csslib", "version": "1.0.2", "dependencies": ["source_span"]}, {"name": "html", "version": "0.15.5", "dependencies": ["csslib", "source_span"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_foundation", "version": "2.4.1", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_android", "version": "2.2.16", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "flutter_inappwebview_windows", "version": "0.6.0", "dependencies": ["flutter", "flutter_inappwebview_platform_interface"]}, {"name": "flutter_inappwebview_web", "version": "1.1.2", "dependencies": ["flutter", "flutter_inappwebview_platform_interface", "flutter_web_plugins", "web"]}, {"name": "flutter_inappwebview_macos", "version": "1.1.2", "dependencies": ["flutter", "flutter_inappwebview_platform_interface"]}, {"name": "flutter_inappwebview_ios", "version": "1.1.2", "dependencies": ["flutter", "flutter_inappwebview_platform_interface"]}, {"name": "flutter_inappwebview_android", "version": "1.1.3", "dependencies": ["flutter", "flutter_inappwebview_platform_interface"]}, {"name": "flutter_inappwebview_platform_interface", "version": "1.3.0+1", "dependencies": ["flutter", "flutter_inappwebview_internal_annotations", "plugin_platform_interface"]}, {"name": "universal_platform", "version": "1.1.0", "dependencies": []}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["flutter"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.4.8", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "image", "version": "4.5.4", "dependencies": ["archive", "meta", "xml"]}, {"name": "cli_util", "version": "0.4.2", "dependencies": ["meta", "path"]}, {"name": "checked_yaml", "version": "2.0.3", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "timezone", "version": "0.10.0", "dependencies": ["http", "path"]}, {"name": "flutter_local_notifications_platform_interface", "version": "8.0.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "flutter_local_notifications_linux", "version": "5.0.0", "dependencies": ["dbus", "ffi", "flutter", "flutter_local_notifications_platform_interface", "path", "xdg_directories"]}, {"name": "firebase_core_web", "version": "2.22.0", "dependencies": ["firebase_core_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "firebase_core_platform_interface", "version": "5.4.0", "dependencies": ["collection", "flutter", "flutter_test", "meta", "plugin_platform_interface"]}, {"name": "firebase_messaging_web", "version": "3.10.5", "dependencies": ["_flutterfire_internals", "firebase_core", "firebase_core_web", "firebase_messaging_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "firebase_messaging_platform_interface", "version": "4.6.5", "dependencies": ["_flutterfire_internals", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "webview_flutter_wkwebview", "version": "3.18.5", "dependencies": ["flutter", "path", "webview_flutter_platform_interface"]}, {"name": "webview_flutter_platform_interface", "version": "2.10.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "webview_flutter_android", "version": "4.3.4", "dependencies": ["flutter", "webview_flutter_platform_interface"]}, {"name": "url_launcher_windows", "version": "3.1.4", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_web", "version": "2.4.0", "dependencies": ["flutter", "flutter_web_plugins", "url_launcher_platform_interface", "web"]}, {"name": "url_launcher_platform_interface", "version": "2.3.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "url_launcher_macos", "version": "3.2.2", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_linux", "version": "3.2.1", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_ios", "version": "6.3.3", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_android", "version": "6.3.15", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "nm", "version": "0.5.0", "dependencies": ["dbus"]}, {"name": "connectivity_plus_platform_interface", "version": "2.0.1", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "synchronized", "version": "3.3.1", "dependencies": []}, {"name": "pointer_interceptor", "version": "0.10.1+2", "dependencies": ["flutter", "flutter_web_plugins", "pointer_interceptor_ios", "pointer_interceptor_platform_interface", "pointer_interceptor_web"]}, {"name": "flutter_keyboard_visibility", "version": "6.0.0", "dependencies": ["flutter", "flutter_keyboard_visibility_linux", "flutter_keyboard_visibility_macos", "flutter_keyboard_visibility_platform_interface", "flutter_keyboard_visibility_web", "flutter_keyboard_visibility_windows", "meta"]}, {"name": "geocoding_ios", "version": "3.0.1", "dependencies": ["flutter", "geocoding_platform_interface"]}, {"name": "geocoding_android", "version": "3.3.1", "dependencies": ["flutter", "geocoding_platform_interface"]}, {"name": "geocoding_platform_interface", "version": "3.2.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "geolocator_windows", "version": "0.2.5", "dependencies": ["flutter", "geolocator_platform_interface"]}, {"name": "geolocator_web", "version": "4.1.3", "dependencies": ["flutter", "flutter_web_plugins", "geolocator_platform_interface", "web"]}, {"name": "geolocator_apple", "version": "2.3.13", "dependencies": ["flutter", "geolocator_platform_interface"]}, {"name": "geolocator_android", "version": "4.6.2", "dependencies": ["flutter", "geolocator_platform_interface", "meta", "uuid"]}, {"name": "geolocator_platform_interface", "version": "4.2.6", "dependencies": ["flutter", "meta", "plugin_platform_interface", "vector_math"]}, {"name": "google_maps_flutter_web", "version": "0.5.12", "dependencies": ["collection", "flutter", "flutter_web_plugins", "google_maps", "google_maps_flutter_platform_interface", "sanitize_html", "stream_transform", "web"]}, {"name": "google_maps_flutter_android", "version": "2.16.0", "dependencies": ["flutter", "flutter_plugin_android_lifecycle", "google_maps_flutter_platform_interface", "stream_transform"]}, {"name": "octo_image", "version": "2.1.0", "dependencies": ["flutter"]}, {"name": "flutter_cache_manager", "version": "3.4.1", "dependencies": ["clock", "collection", "file", "flutter", "http", "path", "path_provider", "rxdart", "sqflite", "uuid"]}, {"name": "cached_network_image_web", "version": "1.3.1", "dependencies": ["cached_network_image_platform_interface", "flutter", "flutter_cache_manager", "web"]}, {"name": "cached_network_image_platform_interface", "version": "4.1.1", "dependencies": ["flutter", "flutter_cache_manager"]}, {"name": "visibility_detector", "version": "0.4.0+2", "dependencies": ["flutter"]}, {"name": "scroll_to_index", "version": "3.0.1", "dependencies": ["flutter"]}, {"name": "photo_view", "version": "0.15.0", "dependencies": ["flutter"]}, {"name": "intl", "version": "0.20.2", "dependencies": ["clock", "meta", "path"]}, {"name": "flutter_parsed_text", "version": "2.2.1", "dependencies": ["flutter"]}, {"name": "flutter_link_previewer", "version": "3.2.2", "dependencies": ["flutter", "flutter_chat_types", "flutter_linkify", "html", "http", "linkify", "meta", "url_launcher"]}, {"name": "flutter_chat_types", "version": "3.6.2", "dependencies": ["equatable", "json_annotation", "meta"]}, {"name": "equatable", "version": "2.0.7", "dependencies": ["collection", "meta"]}, {"name": "diffutil_dart", "version": "4.0.1", "dependencies": []}, {"name": "universal_io", "version": "2.2.2", "dependencies": ["collection", "meta", "typed_data"]}, {"name": "container_tab_indicator", "version": "0.3.0", "dependencies": ["flutter"]}, {"name": "archive", "version": "4.0.5", "dependencies": ["crypto", "path", "posix"]}, {"name": "vector_graphics_compiler", "version": "1.1.16", "dependencies": ["args", "meta", "path", "path_parsing", "vector_graphics_codec", "xml"]}, {"name": "vector_graphics_codec", "version": "1.1.13", "dependencies": []}, {"name": "vector_graphics", "version": "1.1.18", "dependencies": ["flutter", "http", "vector_graphics_codec"]}, {"name": "image_picker_windows", "version": "0.2.1+1", "dependencies": ["file_selector_platform_interface", "file_selector_windows", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_platform_interface", "version": "2.10.1", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "image_picker_macos", "version": "0.2.1+2", "dependencies": ["file_selector_macos", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_linux", "version": "0.2.1+2", "dependencies": ["file_selector_linux", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_ios", "version": "0.8.12+2", "dependencies": ["flutter", "image_picker_platform_interface"]}, {"name": "image_picker_for_web", "version": "3.0.6", "dependencies": ["flutter", "flutter_web_plugins", "image_picker_platform_interface", "mime", "web"]}, {"name": "image_picker_android", "version": "0.8.12+22", "dependencies": ["flutter", "flutter_plugin_android_lifecycle", "image_picker_platform_interface"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "leak_tracker_testing", "version": "3.0.2", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "win32", "version": "5.12.0", "dependencies": ["ffi"]}, {"name": "package_info_plus_platform_interface", "version": "3.2.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "flutter_inappwebview_internal_annotations", "version": "1.2.0", "dependencies": []}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "xml", "version": "6.5.0", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "dbus", "version": "0.7.11", "dependencies": ["args", "ffi", "meta", "xml"]}, {"name": "_flutterfire_internals", "version": "1.3.54", "dependencies": ["collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "pointer_interceptor_web", "version": "0.10.2+1", "dependencies": ["flutter", "flutter_web_plugins", "plugin_platform_interface", "pointer_interceptor_platform_interface", "web"]}, {"name": "pointer_interceptor_platform_interface", "version": "0.10.0+1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "pointer_interceptor_ios", "version": "0.10.1", "dependencies": ["flutter", "plugin_platform_interface", "pointer_interceptor_platform_interface"]}, {"name": "flutter_keyboard_visibility_windows", "version": "1.0.0", "dependencies": ["flutter", "flutter_keyboard_visibility_platform_interface"]}, {"name": "flutter_keyboard_visibility_web", "version": "2.0.0", "dependencies": ["flutter", "flutter_keyboard_visibility_platform_interface", "flutter_web_plugins"]}, {"name": "flutter_keyboard_visibility_macos", "version": "1.0.0", "dependencies": ["flutter", "flutter_keyboard_visibility_platform_interface"]}, {"name": "flutter_keyboard_visibility_linux", "version": "1.0.0", "dependencies": ["flutter", "flutter_keyboard_visibility_platform_interface"]}, {"name": "flutter_keyboard_visibility_platform_interface", "version": "2.0.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "sanitize_html", "version": "2.1.0", "dependencies": ["html", "meta"]}, {"name": "google_maps", "version": "8.1.1", "dependencies": ["meta", "web"]}, {"name": "flutter_plugin_android_lifecycle", "version": "2.0.27", "dependencies": ["flutter"]}, {"name": "sqflite", "version": "2.4.2", "dependencies": ["flutter", "path", "sqflite_android", "sqflite_common", "sqflite_darwin", "sqflite_platform_interface"]}, {"name": "rxdart", "version": "0.28.0", "dependencies": []}, {"name": "linkify", "version": "5.0.0", "dependencies": []}, {"name": "flutter_linkify", "version": "6.0.0", "dependencies": ["flutter", "linkify"]}, {"name": "posix", "version": "6.0.1", "dependencies": ["ffi", "meta", "path"]}, {"name": "path_parsing", "version": "1.1.0", "dependencies": ["meta", "vector_math"]}, {"name": "file_selector_windows", "version": "0.9.3+4", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_platform_interface", "version": "2.6.2", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "cross_file", "version": "0.3.4+2", "dependencies": ["meta", "web"]}, {"name": "file_selector_macos", "version": "0.9.4+2", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_linux", "version": "0.9.3+2", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "petitparser", "version": "6.1.0", "dependencies": ["collection", "meta"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "sqflite_common", "version": "2.5.5", "dependencies": ["meta", "path", "synchronized"]}, {"name": "sqflite_platform_interface", "version": "2.4.0", "dependencies": ["flutter", "meta", "platform", "plugin_platform_interface", "sqflite_common"]}, {"name": "sqflite_darwin", "version": "2.4.2", "dependencies": ["flutter", "meta", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "sqflite_android", "version": "2.4.1", "dependencies": ["flutter", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "leak_tracker", "version": "11.0.2", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}], "configVersion": 1}